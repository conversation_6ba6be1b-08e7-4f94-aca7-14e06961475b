using System.Xml.Serialization;

namespace Siepe.aim.instrument
{
    /// <summary>
    /// POCO class for deserializing AIM Security Master Feed XML response
    /// </summary>
    [XmlRoot("SMFMessage")]
    public class AIMSecurityMasterFeed
    {
        [XmlAttribute("pricingNumber")]
        public int PricingNumber { get; set; }

        [XmlAttribute("date")]
        public DateTime Date { get; set; }

        [XmlElement("Field")]
        public List<AIMField> Fields { get; set; } = new List<AIMField>();
    }

    /// <summary>
    /// Represents a field in the AIM Security Master Feed XML
    /// </summary>
    public class <PERSON>MField
    {
        [XmlAttribute("calcrt")]
        public string CalCrt { get; set; } = string.Empty;

        [XmlAttribute("name")]
        public string Name { get; set; } = string.Empty;

        [XmlText]
        public string Value { get; set; } = string.Empty;
    }
}
