using Siepe.Core.DataAccess.RefData;
using Siepe.Core.Entities;
using InstSvc=Instruments.Entities;
using Siepe.Core.DataAccess;
using System.Linq;
using System.Text.Json;
using Siepe.Core.Entities.Logging;
using System.Runtime.CompilerServices;
using System.Xml.Serialization;
using System.Globalization;
using System.Data;
using System.Data.SqlClient;
using Siepe.Shared.DBUtility.v1;
using Siepe.Core.Entities.OMS;

namespace Siepe.aim.instrument
{

    public class InstrumentManager
    {

        public IRefDataProvider _refDataProvider { get; set; }
        public IDbAccess _dbAccess { get; set; }

        public InstrumentManager(IRefDataProvider refDataProvider, IDbAccess dbAccess)
        {
            this._refDataProvider = refDataProvider;
            this._dbAccess = dbAccess;
        }

        public InstSvc.IInstrument? SaveInstrument(InstSvc.Instrument inst)
        {
            int refInstId;
            if (inst.Issuer?.Name != null)
            {
                if (inst.Issuer.DataSource == null) 
                    inst.Issuer.DataSource = new InstSvc.DataSource();
                if (String.IsNullOrWhiteSpace(inst.Issuer.DataSource.DataSourceKey))
                    inst.Issuer.DataSource.DataSourceKey = inst.Issuer.Name;
            }
            if (inst is InstSvc.Debt)
            {
                var d = inst as InstSvc.Debt;
                d.MaturityDate = getDatePart(d.MaturityDate);
            }
            if (inst is InstSvc.ABS)
            {
                var d = inst as InstSvc.ABS;
                d.MaturityDate = getDatePart(d.MaturityDate);
            }
            if (inst is InstSvc.CashFlow)
            {
                var d = inst as InstSvc.CashFlow;
                d.MaturityDate = getDatePart(d.MaturityDate);
            }
            if (inst is InstSvc.CDS)
            {
                var d = inst as InstSvc.CDS;
                d.MaturityDate = getDatePart(d.MaturityDate);
            }
            if (inst is InstSvc.SwapFinanceLeg)
            {
                var d = inst as InstSvc.SwapFinanceLeg;
                d.MaturityDate = getDatePart(d.MaturityDate);
            }
            if (inst is InstSvc.Bond)
            {
                var b = inst as InstSvc.Bond;
                b.LastResetDate = getDatePart(b.LastResetDate);
            }
            if (inst != null)
            {
                logMessage("Inst Type - " + inst.GetType().ToString() + " " + inst.InstrumentType.ToString());
            }
            //*******SAVE INSTRUMENT IN REFERENCE *************************************************************************************************
            if (inst.Issuer?.Id <= 0)
                inst.Issuer.Id = null;
            refInstId = SaveInstrumentInRef(inst);
            logMessage("Ref Inst Id - " + refInstId.ToString());

            //*******PROMOTE INSTRUMENT FROM REFERENCE TO CREATE IN CORE***************************************************************************
            
            string coreInstrumentType = inst.CoreInstrumentType?.Name ?? inst.InstrumentType?.Name;
            var id = inst.CoreInstrumentType?.Id;
            if (inst.CoreInstrumentType?.Id != null)
            {
                var idType = _refDataProvider.GetInstTypes().FirstOrDefault(o => o.InstTypeID == id);
                if (idType != null)
                    coreInstrumentType = idType.Name;
            }
            var coreInstId = CreateInstrumentFromReference(refInstId, coreInstrumentType);
            
            //********MAP INSTRUMENT IDs BETWEEN CORE AND REFERENCE********************************************************************************

            var rInst = _refDataProvider.GetInstMapToCore(refInstId).FirstOrDefault();
            if (rInst != null)
                coreInstId = rInst.InstID;
            logMessage($"Core Inst Id: {coreInstId} : RefInstId: {refInstId} {coreInstrumentType}");
            try
            {
                var instrument = _refDataProvider.GetIInstrument(coreInstId);
                return instrument;
            }
            catch (Exception ex)
            {
                logMessage($"Error retrieving instrument with CoreInstId: {coreInstId}. Exception: {ex.Message}");
                throw;
            }
        }
        private DateTime? getDatePart(DateTime? dt)
        {
            if (dt.HasValue == false) return dt;
            if (dt.Value == DateTime.MinValue) return null;
            if (dt.Value == DateTime.MaxValue) return null;
            if (dt.Value.Year < 100) return null;
            return dt.Value.Date;
        }
        private int SaveInstrumentInRef(InstSvc.Instrument inst)
        {
            var dataSource = inst?.DataSource?.Name;
            if (String.IsNullOrWhiteSpace(dataSource))
                dataSource = "Client Underride";
            return _refDataProvider.CreateInstrumentInRef(inst, dataSource);
        }

        private int CreateInstrumentFromReference(int instIdRef, string coreInstrumentType)
        {
            return _refDataProvider.CreateInstrumentFromRef(instIdRef, coreInstrumentType);
        }

        private void logMessage(string message, [CallerMemberName] string location = "")
        {
            Logger.WriteDiagnostics(new LogDetail()
            {
                Message = message,
                Product = "BloombergRawMessage.InstrumentManager",
                Location = location,
            });
        }

        /// <summary>
        /// Gets or creates an instrument based on Bloomberg AIM trade data identifiers.
        /// Checks if instrument exists in OMS first, if not found, fetches from AIM Security Master feed and creates it.
        /// </summary>
        /// <param name="identifierValue">The identifier value (e.g., Bloomberg Unique ID)</param>
        /// <param name="identifierType">The type of identifier (e.g., "BB_UNIQUE", "CUSIP", "ISIN")</param>
        /// <param name="tradeDate">The trade date for the instrument lookup</param>
        /// <returns>InstrumentInfo for the existing or newly created instrument</returns>
        public InstrumentInfo? GetOrCreateInstrument(string identifierValue, string identifierType, DateTime tradeDate)
        {
            try
            {
                logMessage($"Starting GetOrCreateInstrument for {identifierType}: {identifierValue}, TradeDate: {tradeDate:yyyy-MM-dd}");

                // Step 1: Check if instrument already exists in OMS
                var existingInstruments = _refDataProvider.SearchInstrument(identifierValue, tradeDate);
                if (existingInstruments != null && existingInstruments.Count > 0)
                {
                    logMessage($"Found existing instrument: {existingInstruments[0].InstID}");
                    return existingInstruments[0];
                }

                logMessage("Instrument not found in OMS, fetching from AIM Security Master feed");

                // Step 2: Fetch AIM Security Master Feed
                var aimSecurity = FetchAIMSecurityMasterFeed(tradeDate, identifierType, identifierValue);
                if (aimSecurity == null)
                {
                    logMessage("No data returned from AIM Security Master feed");
                    return null;
                }

                // Step 3: Map AIM feed to Instrument entity
                var newInstrument = CreateInst(aimSecurity);
                if (newInstrument == null)
                {
                    logMessage("Failed to map AIM feed data to Instrument entity");
                    return null;
                }

                // Step 4: Save the new instrument using existing logic
                var savedInstrument = SaveInstrument(newInstrument);
                if (savedInstrument == null)
                {
                    logMessage("Failed to save new instrument");
                    return null;
                }

                // Step 5: Return the newly created instrument info
                // We need to search again to get the InstrumentInfo with proper IDs
                var newInstrumentInfos = _refDataProvider.SearchInstrument(identifierValue, tradeDate);
                if (newInstrumentInfos != null && newInstrumentInfos.Count > 0)
                {
                    logMessage($"Successfully created new instrument: {newInstrumentInfos[0].InstID}");
                    return newInstrumentInfos[0];
                }

                logMessage("Warning: Instrument was saved but could not be retrieved via search");
                return null;
            }
            catch (Exception ex)
            {
                logMessage($"Error in GetOrCreateInstrument: {ex.Message}");
                throw;
            }
        }
        /// <summary>
        /// Creates an InstSvc.Instrument object from AIMSecurityMasterFeed data.
        /// </summary>
        /// <param name="aimInst"></param>
        /// <returns></returns>
        private InstSvc.Instrument CreateInst(AIMSecurityMasterFeed aimInst)
        {
            throw new NotImplementedException();
            //return new InstrumentInfo();
        }

        /// <summary>
        /// Fetches AIM Security Master Feed data by executing the stored procedure pGetAIMSecurityMasterFeedMessage
        /// </summary>
        /// <param name="tradeDate">The trade date</param>
        /// <param name="identifierType">The identifier type (e.g., "BB_UNIQUE", "CUSIP", "ISIN")</param>
        /// <param name="identifierValue">The identifier value</param>
        /// <returns>Deserialized AIMSecurityMasterFeed object or null if no data found</returns>
        private AIMSecurityMasterFeed? FetchAIMSecurityMasterFeed(DateTime tradeDate, string identifierType, string identifierValue)
        {
            try
            {
                logMessage($"Executing pGetAIMSecurityMasterFeedMessage with TradeDate: {tradeDate:yyyy-MM-dd}, Type: {identifierType}, Value: {identifierValue}");

                var parameters = new[]
                {
                    new SqlParameter("@tradeDate", SqlDbType.DateTime) { Value = tradeDate },
                    new SqlParameter("@identifierType", SqlDbType.VarChar, 20) { Value = identifierType },
                    new SqlParameter("@identifierValue", SqlDbType.VarChar, 20) { Value = identifierValue }
                };

                string? xmlResult = null;
                _dbAccess.ExecuteReader("pGetAIMSecurityMasterFeedMessage",
                    parameters,
                    CommandType.StoredProcedure,
                    withReaderAction: (reader, command, connection) =>
                    {
                        if (reader.Read())
                        {
                            xmlResult = reader[0]?.ToString();
                        }
                    });

                if (string.IsNullOrWhiteSpace(xmlResult))
                {
                    logMessage("No XML data returned from stored procedure");
                    return null;
                }

                logMessage($"Received XML data: {xmlResult.Substring(0, Math.Min(200, xmlResult.Length))}...");

                // Deserialize XML to AIMSecurityMasterFeed object
                var serializer = new XmlSerializer(typeof(AIMSecurityMasterFeed));
                using (var stringReader = new StringReader(xmlResult))
                {
                    var result = (AIMSecurityMasterFeed)serializer.Deserialize(stringReader);
                    logMessage($"Successfully deserialized AIM feed with {result?.Fields?.Count ?? 0} fields");
                    return result;
                }
            }
            catch (Exception ex)
            {
                logMessage($"Error in FetchAIMSecurityMasterFeed: {ex.Message}");
                throw;
            }
        }

    

        /// <summary>
        /// Helper method to safely get field value from AIM feed
        /// </summary>
        private string? GetFieldValue(List<AIMField> fields, string fieldName)
        {
            return fields.FirstOrDefault(f => f.Name == fieldName)?.Value;
        }

        /// <summary>
        /// Helper method to safely parse date fields from AIM feed
        /// </summary>
      private DateTime? ParseDateField(List<AIMField>? fields, string fieldName)
    {
        var dateValue = GetFieldValue(fields, fieldName);
        if (string.IsNullOrWhiteSpace(dateValue)) return null;

        if (DateTime.TryParseExact(dateValue, "yyyyMMdd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var result))
            return result;

        logMessage($"Failed to parse date field {fieldName}: {dateValue}");
        return null;
    }


    }
}