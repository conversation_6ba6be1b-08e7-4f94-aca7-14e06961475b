using Siepe.Shared.DBUtility;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace BBGRaw.Console.QA
{
    class Program
    {
        static void Main(string[] args)
        {

            var sqlDbAccess = new SqlDbAccess("FeedsConnectionString");

            var blocks = new List<string>();
            sqlDbAccess.ExecuteReader("SELECT CAST(OriginalXml as nvarchar(max)) FROM Aim.tDailyExecutionBlock WHERE EventDate = '2021-03-25' AND RefRecStatusID = 1",
                commandType: System.Data.CommandType.Text,
                withReaderAction: (reader, command, connection) =>
                {
                    while (reader.Read())
                    {
                        var b = (string)reader[0];
                        blocks.Add(b);
                    }
                });

            var allocations = new List<string>();
            sqlDbAccess.ExecuteReader("SELECT CAST(OriginalXml as nvarchar(max)) FROM Aim.tDailyExecutionRaw WHERE EventDate = '2021-03-25' AND RefRecStatusID = 1",
                commandType: System.Data.CommandType.Text,
                withReaderAction: (reader, command, connection) =>
                {
                    while (reader.Read())
                    {
                        var a = (string)reader[0];
                        allocations.Add(a);
                    }
                });


            var client = new BBGRawSvc.ServicePublicationClient("WSHttpBinding_IServicePublication");

            var i = 0;
            foreach (var b in blocks)
            {
                client.Publish(new BBGRawSvc.Message
                {
                    BaseSubject = "TradeFee.RawMessage",
                    Payload = Encoding.UTF8.GetBytes(b)
                });

                System.Console.WriteLine(i++);
                Thread.Sleep(1000);
            }

            i = 0;
            foreach (var a in allocations)
            {
                client.Publish(new BBGRawSvc.Message
                {
                    BaseSubject = "TradeFee.RawMessage",
                    Payload = Encoding.UTF8.GetBytes(a)
                });

                System.Console.WriteLine(i++);
                Thread.Sleep(1000);
            }
        }

        private static string GetTestXml()
        {
            return File.ReadAllText("TestInput.xml");
        }
    }
}
